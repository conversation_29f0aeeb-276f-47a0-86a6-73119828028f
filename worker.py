import logging
import os
import re
from logging.config import dictConfig
from typing import List, Dict, <PERSON>ple

import redis
import requests
from MailChecker import <PERSON><PERSON><PERSON><PERSON>
from celery import Celery
from pymongo import MongoClient
from pymongo.collection import Collection
from redis import Redis
from retry import retry
from unidecode import unidecode

import config
from verifier import MailVerifier
import utils

# ------------------------- SETUP SECTION -------------------------


dictConfig(config.LogConfig().dict())
logger = logging.getLogger("celery")

redis_host: str = os.environ['FINDVERIFY_REDIS_HOST']
redis_port: str = os.environ['FINDVERIFY_REDIS_PORT']
redis_celery_url: str = f"redis://{redis_host}:{redis_port}"

mongo_host: str = os.environ['FINDVERIFY_MONGO_HOST']
mongo_port: int = int(os.environ['FINDVERIFY_MONGO_PORT'])

celery = Celery(__name__)
celery.conf.broker_url = os.environ.get(
    "CELERY_BROKER_URL",
    redis_celery_url
)
celery.conf.result_backend = os.environ.get(
    "CELERY_RESULT_BACKEND",
    redis_celery_url
)


def verify_email(email_id: str, from_emails_list: List[str], redis_instance: Redis) -> Tuple[str, Dict | None]:
    """
    Verifies given email and returns a Tuple of email + result dictionary. In case of bad email, returns None in place
    of result dictionary.

    :param email_id: Email address (ex. <EMAIL>)
    :param from_emails_list: List of email addresses to use as FROM address during verification.
    :param redis_instance: Redis instance for storing greylisted emails.
    """
    try:
        # Remove unicode
        email_id = unidecode(email_id).lstrip().rstrip()

        # Check if it is in valid format
        if not MailChecker.is_valid_email_format(email_id):
            return email_id, None

        # Initialize MailVerifier class and obtain the smtp_instance
        try:
            mail_verifier = MailVerifier(from_email_list=from_emails_list, redis_instance=redis_instance)
            result = mail_verifier.verify_mail(email_id)
            return email_id, result

        except Exception as err:
            logger.critical(f"Mail Verifier Error (Bulk Verify): {err}", exc_info=True)
            return email_id, None

    # Main exception catcher - so that entire task doesn't fail because of one error.
    except Exception as err:
        logger.critical(err, exc_info=True)
        return email_id, None


def find_email(fname: str, lname: str, domain: str,
               from_emails_list: List[str], redis_instance: Redis) -> Tuple[List[str], Dict | None]:
    """
    Finds email from given first name, last name and domain. Returns a Tuple of [fn, ln, domain] + result dictionary.
    In case of bad details, returns None in place of user details list.

    :param fname: First name (ex. John)
    :param lname: Last name (ex. Doe)
    :param domain: Domain (ex. example.com)
    :param from_emails_list: List of email addresses to use as FROM address during verification.
    :param redis_instance: Redis instance for storing greylisted emails.
    """
    # Convert domain to required format.
    domain: str = re.sub(r"^(www.|https?://www.|https?://)", "", domain, re.IGNORECASE).rstrip('/')

    # Check if all values are available.
    # We need both or one of firstname & lastname, and domain, to find emails.
    if not ((fname or lname) and domain):
        return [fname, lname, domain], None

    # Run find email.
    try:
        mail_verifier = MailVerifier(from_email_list=from_emails_list, redis_instance=redis_instance)
        result = mail_verifier.find_mail(fname, lname, domain)
    except Exception as err:
        logger.critical(err, exc_info=True)
        return [fname, lname, domain], None

    return [fname, lname, domain], result


@celery.task
def test_task(a: int, b: int):
    print(f"a + b is {a + b}")
    return a + b


@celery.task(bind=True, max_retries=3)
def single_verify_task(self, email: str, from_emails_list: List[str], redis_connection_url: str,
                      wait_for_result: bool = True, document_id: str = None, retry_count: int = 0):
    """
    Single email verification with greylist retry support

    :param email: Email address to verify
    :param from_emails_list: List of email addresses to use as FROM
    :param redis_connection_url: Redis connection URL
    :param wait_for_result: Whether to wait for result or return immediately
    :param document_id: MongoDB document ID for updates
    :param retry_count: Current retry count
    """
    logger.info(f"[*] Starting single verify task for {email} (retry: {retry_count})...")

    # Connect to Redis
    try:
        redis_pool = redis.ConnectionPool.from_url(redis_connection_url)
        redis_instance = redis.Redis(connection_pool=redis_pool, socket_connect_timeout=120)
    except Exception as err:
        logger.error("Single Verify: Could not connect to Redis instance.")
        if wait_for_result:
            raise self.retry(exc=err)
        return {"error": "Redis connection failed"}

    try:
        # Perform verification
        email_clean, result = verify_email(email, from_emails_list, redis_instance)

        if result is None:
            result = {
                "error": True,
                "message": "Verification failed",
                "email": email
            }

        # Add retry information
        result['retry_count'] = retry_count
        result['is_retry_task'] = retry_count > 0

        # Save or update in MongoDB
        if document_id:
            # Update existing document
            utils.update_single_verification(document_id, result, retry_count)
        else:
            # Save new document
            document_id = utils.save_single_verification(email, 'verify', result, retry_count)
            result['mongodb_id'] = document_id

        # Check if greylist and should retry
        if utils.should_retry_greylist(result) and retry_count < 3:
            delay = utils.get_retry_delay(retry_count)
            logger.info(f"Greylist detected for {email}, scheduling retry {retry_count + 1} in {delay}s")

            # Schedule next retry
            self.retry(
                args=[email, from_emails_list, redis_connection_url, False, document_id, retry_count + 1],
                countdown=delay
            )
        elif utils.should_retry_greylist(result) and retry_count >= 3:
            # Max retries reached, mark as catch-all
            result = utils.mark_as_catch_all_after_retries(result, retry_count)
            utils.update_single_verification(document_id, result, retry_count)
            logger.info(f"Max retries reached for {email}, marked as catch-all")

        logger.info(f"[*] Single verify task for {email} completed!")
        return result

    except Exception as err:
        logger.critical(f"Single verify task error: {err}", exc_info=True)
        error_result = {
            "error": True,
            "message": f"Task error: {str(err)}",
            "email": email,
            "retry_count": retry_count
        }

        if document_id:
            utils.update_single_verification(document_id, error_result, retry_count)

        if wait_for_result:
            raise err
        return error_result


@celery.task(bind=True, max_retries=3)
def single_find_task(self, firstname: str, lastname: str, domain: str, from_emails_list: List[str],
                    redis_connection_url: str, wait_for_result: bool = True,
                    document_id: str = None, retry_count: int = 0):
    """
    Single email finding with greylist retry support

    :param firstname: First name
    :param lastname: Last name
    :param domain: Domain
    :param from_emails_list: List of email addresses to use as FROM
    :param redis_connection_url: Redis connection URL
    :param wait_for_result: Whether to wait for result or return immediately
    :param document_id: MongoDB document ID for updates
    :param retry_count: Current retry count
    """
    email_identifier = f"{firstname}.{lastname}@{domain}" if lastname else f"{firstname}@{domain}"
    logger.info(f"[*] Starting single find task for {email_identifier} (retry: {retry_count})...")

    # Connect to Redis
    try:
        redis_pool = redis.ConnectionPool.from_url(redis_connection_url)
        redis_instance = redis.Redis(connection_pool=redis_pool, socket_connect_timeout=120)
    except Exception as err:
        logger.error("Single Find: Could not connect to Redis instance.")
        if wait_for_result:
            raise self.retry(exc=err)
        return {"error": "Redis connection failed"}

    try:
        # Perform find
        user_details, result = find_email(firstname, lastname, domain, from_emails_list, redis_instance)

        if result is None:
            result = {
                "error": True,
                "message": "Find operation failed",
                "user_details": user_details
            }

        # Add retry information
        result['retry_count'] = retry_count
        result['is_retry_task'] = retry_count > 0

        # Save or update in MongoDB
        if document_id:
            # Update existing document
            utils.update_single_verification(document_id, result, retry_count)
        else:
            # Save new document
            document_id = utils.save_single_verification(email_identifier, 'find', result, retry_count)
            result['mongodb_id'] = document_id

        # Check if greylist and should retry
        if utils.should_retry_greylist(result) and retry_count < 3:
            delay = utils.get_retry_delay(retry_count)
            logger.info(f"Greylist detected for {email_identifier}, scheduling retry {retry_count + 1} in {delay}s")

            # Schedule next retry
            self.retry(
                args=[firstname, lastname, domain, from_emails_list, redis_connection_url,
                     False, document_id, retry_count + 1],
                countdown=delay
            )
        elif utils.should_retry_greylist(result) and retry_count >= 3:
            # Max retries reached, mark as catch-all
            result = utils.mark_as_catch_all_after_retries(result, retry_count)
            utils.update_single_verification(document_id, result, retry_count)
            logger.info(f"Max retries reached for {email_identifier}, marked as catch-all")

        logger.info(f"[*] Single find task for {email_identifier} completed!")
        return result

    except Exception as err:
        logger.critical(f"Single find task error: {err}", exc_info=True)
        error_result = {
            "error": True,
            "message": f"Task error: {str(err)}",
            "user_details": [firstname, lastname, domain],
            "retry_count": retry_count
        }

        if document_id:
            utils.update_single_verification(document_id, error_result, retry_count)

        if wait_for_result:
            raise err
        return error_result


# noinspection PyIncorrectDocstring
@celery.task(bind=True, default_retry_delay=60, max_retries=5, retry_backoff=True)
def bulk_verify_task(self, email_list: List[str], from_emails_list: List[str], redis_connection_url: str,
                     task_uid: str, webhook_url: str | None):
    """
    Bulk verifies given list of email addresses.

    :param email_list: List of email ids.
    :param from_emails_list: List of email ids for use as FROM address during email verification.
    :param redis_connection_url: Redis connection url.
    :param task_uid: results will be stored in MongoDB using this value as identifier.
    """
    logger.info(f"[*] Starting bulk verify task for {task_uid}...")

    # Connect to Redis.
    try:
        redis_pool = redis.ConnectionPool.from_url(redis_connection_url)
        redis_instance = redis.Redis(connection_pool=redis_pool, socket_connect_timeout=120)
    except Exception as err:
        logger.error("Bulk Verify: Could not connect to Redis instance.")
        raise self.retry(exc=err)

    # Connect to MongoDB.
    try:
        mongo_client = MongoClient(mongo_host, mongo_port)
        mongo_db = mongo_client["findverify_db"]
        bulk_verify_tasks_collection: Collection = mongo_db["bulk_verify_tasks"]
    except Exception as err:
        logger.error("Bulk Verify: Could not connect to MongoDB instance.")
        raise self.retry(exc=err)

    try:
        # Use the new retry mechanism for bulk processing
        results = utils.process_bulk_with_retries(
            email_list, 'verify', from_emails_list, redis_instance, task_uid
        )

        bulk_verify_tasks_collection.update_one(
            {"task_uid": task_uid},
            {"$set": {"results": results, "status": "completed"}},
        )

        logger.info(f"[*] Bulk verify task for {task_uid} successful!")

        if webhook_url:
            send_webhook(webhook_url, {"results": results, "task_uid": task_uid})
            logger.info(f"Results sent to webhook url {webhook_url}")

    except Exception as err:
        # Mark this task as failed.
        logger.critical(err, exc_info=True)
        bulk_verify_tasks_collection.update_one(
            {"task_uid": task_uid},
            {"$set": {"status": "failed"}},
        )


@retry(tries=5, backoff=2)
def send_webhook(url: str, data: Dict):
    wh_res = requests.post(
        url,
        json=data
    )
    if wh_res.status_code != 200:
        raise Exception(f"Webhook failed with status code {wh_res.status_code}")


# noinspection PyIncorrectDocstring
@celery.task(bind=True, default_retry_delay=60, max_retries=5, retry_backoff=True)
def bulk_find_task(self, userdetails_list: List[List[str]], from_emails_list: List[str],
                   redis_connection_url: str, task_uid: str):
    """
    Bulk verifies given list of email addresses.

    :param userdetails_list: List of userdetails [["fn", "ln", "domain"], ...].
    :param from_emails_list: List of email ids for use as FROM address during email verification.
    :param redis_connection_url: Redis connection url.
    :param task_uid: results will be stored in MongoDB using this value as identifier.
    """
    logger.info(f"[*] Starting bulk find task for {task_uid}...")

    # Connect to Redis.
    try:
        redis_pool = redis.ConnectionPool.from_url(redis_connection_url)
        redis_instance = redis.Redis(connection_pool=redis_pool, socket_connect_timeout=120)
    except Exception as err:
        logger.error("Bulk Find: Could not connect to Redis instance.")
        raise self.retry(exc=err)

    # Connect to MongoDB.
    try:
        mongo_client = MongoClient(mongo_host, mongo_port)
        mongo_db = mongo_client["findverify_db"]
        bulk_find_tasks_collection: Collection = mongo_db["bulk_find_tasks"]
    except Exception as err:
        logger.error("Bulk Find: Could not connect to MongoDB instance.")
        raise self.retry(exc=err)

    try:
        # Use the new retry mechanism for bulk processing
        results = utils.process_bulk_with_retries(
            userdetails_list, 'find', from_emails_list, redis_instance, task_uid
        )

        bulk_find_tasks_collection.update_one(
            {"task_uid": task_uid},
            {"$set": {"results": results, "status": "completed"}},
        )

        logger.info(f"[*] Bulk find task for {task_uid} successful!")

    except Exception as err:
        # Mark this task as failed.
        logger.critical(err, exc_info=True)
        bulk_find_tasks_collection.update_one(
            {"task_uid": task_uid},
            {"$set": {"status": "failed"}},
        )
