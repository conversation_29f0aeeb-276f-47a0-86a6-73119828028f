import binascii
import os
import random
import re
import socket
from socket import timeout
import logging
import uuid

import smtplib
from collections import namedtuple
from smtplib import SMTP
from typing import Dict, List, Optional, Any

from dns import resolver
from dns.exception import Timeout as DNSTimeout
from redis import Redis

import utils
from worker import single_verify_task, single_find_task
from worker import bulk_verify_task

logger = logging.getLogger("mail_verifier_logger")
logger.setLevel(logging.ERROR)


class SMTPRecipientException(Exception):  # don't cover
    def __init__(self, code, response):
        self.code = code
        self.response = response


socket.setdefaulttimeout(30)


# Some of the RCPT error status codes returned by SMTP servers
other_errors = {
    551: lambda _: dict(deliverable=False,
                        host_exists=True,
                        message="Status: 551 - Mailbox does not exists on this domain"),
    552: lambda _: dict(deliverable=True,
                        host_exists=True,
                        full_inbox=True,
                        message="Status: 552 - Their mailbox does not have enough storage currently"),
    553: lambda _: dict(deliverable=False,
                        host_exists=True,
                        message="Status: 553 - Mailbox with given name does not exists on this domain"),
    450: lambda _: dict(deliverable=False,
                        host_exists=True,
                        message="Status: 450 - Their mailbox is locked or not routable"),
    451: lambda _: dict(deliverable=False,
                        message="Status: 451 - Mail server of provided domain is currently facing some issues"),
    452: lambda _: dict(deliverable=False,
                        full_inbox=True,
                        message="Status: 452 - Message is deferred until storage opens up"),
    521: lambda _: dict(deliverable=False,
                        host_exists=False,
                        message="Status: 521 - This domain does not accept emails"),
    421: lambda _: dict(deliverable=False,
                        host_exists=True,
                        message="Status: 421 - Email service not available, try again later."),
    550: lambda _: dict(deliverable=False,
                        host_exists=True,
                        message="Status: 550 - Mailbox does not exists."),
}


def handle_unrecognized_error(_error):
    return dict(deliverable=False, message="You can't send messages to this Email ID")


def handle_connection_error(_lookup: Dict):
    """
    returns lookup data for socket connection closed events
    :param _lookup:
    :return:
    """
    _lookup['deliverable'] = False
    _lookup['catch_all'] = False
    _lookup['message'] = "You can't send messages to this Email ID"
    _lookup['debug_message'] = "Connection closed by target server"

    return _lookup


# For storing email address data
Address = namedtuple('Address', 'email, domain')


class MailVerifier:
    def __init__(self, from_email_list: List[str], redis_instance: Redis | None = None,
                 enable_greylist_retry: bool = True, save_to_mongodb: bool = True):
        self.redis_greylist_instance = redis_instance
        self.mx_record = ""
        self.greylist_strings = [b"blacklisted",
                                 b"spamhaus.org/sbl/",
                                 b"Greylisted",
                                 b"greylisted",
                                 b"reverse",
                                 b"allowed",
                                 b"Unable",
                                 b"blocked",
                                 b"Access denied",
                                 b"5.4.1",
                                 b"SPF",
                                 b"5.1.8",
                                 b"5.7.705",
                                 b"IB607",
                                 b"IB607",
                                 b"IB510",
                                 b"IB113",
                                 b"IB111",
                                 b"IB110",
                                 b"IB007",
                                 b"IB605",
                                 b"IB705",
                                 b"IB212",
                                 b"spam",
                                 b"virus",
                                 b"temporary",
                                 b"temporarily",
                                 b"many",
                                 b"spoofing",
                                 b"5.3.0",
                                 b"relay not permitted",
                                 b"Rule imposed mailbox access",
                                 b"authentication",
                                 b"***************",
                                 b"We don't handle mail",
                                 b"dns",
                                 b"minutes",
                                 b"hour",
                                 b"5.7.64",
                                 b"This domain is not hosted here",
                                 b"5.2.2"
                                 b"5.2.1",
                                 b"5.7.1",
                                 b'Message rejected']

        # other codes (allowed for now) -> 420, 421, 431, 441, 442, 446, 447, 450, 451, 452, 454, 455, 471,
        self.greylist_codes = [503, 521, 523, 535, 541, 553, 554, 557]

        self.redis_greylist_record_expiry = 3200  # In seconds

        self.email_pool = from_email_list

        # Greylist retry configuration
        self.enable_greylist_retry = enable_greylist_retry
        self.save_to_mongodb = save_to_mongodb

        # success response codes that we are assuming for various mail servers
        self.rcpt_success_codes = {
            'google': [250, 450],
            'outlook': [250],
            'others': [250]
        }

        # Google/Gmail MX record patterns for ESP detection
        self.google_mx_patterns = [
            'gmail-smtp-in.l.google.com',
            'aspmx.l.google.com',
            'alt1.aspmx.l.google.com',
            'alt2.aspmx.l.google.com',
            'alt3.aspmx.l.google.com',
            'alt4.aspmx.l.google.com',
            'aspmx2.googlemail.com',
            'aspmx3.googlemail.com',
            'aspmx4.googlemail.com',
            'aspmx5.googlemail.com'
        ]

        self.SOCKET_RESP_BUFFER_SIZE = 1024

    def _detect_esp(self, mx_record: str) -> str:
        """
        Detects the Email Service Provider based on MX record
        :param mx_record: MX record string (e.g., 'aspmx.l.google.com.')
        :return: ESP name ('google', 'outlook', 'others')
        """
        mx_record_lower = mx_record.lower()

        # Check for Google/Gmail patterns
        for pattern in self.google_mx_patterns:
            if pattern in mx_record_lower:
                return 'google'

        # Check for Outlook/Microsoft patterns
        outlook_patterns = [
            'outlook.com',
            'hotmail.com',
            'live.com',
            'mail.protection.outlook.com',
            'ppe-hosted.com'
        ]

        for pattern in outlook_patterns:
            if pattern in mx_record_lower:
                return 'outlook'

        return 'others'

    def _handle_greylist_response(self, email: str, verification_type: str, result: Dict[str, Any],
                                wait_for_result: bool = True) -> Dict[str, Any]:
        """
        Handle greylist response by saving to MongoDB and starting Celery retry
        :param email: Email address
        :param verification_type: 'verify' or 'find'
        :param result: Current verification result
        :param wait_for_result: Whether to wait for result or return immediately
        :return: Updated result with retry information
        """
        if not self.enable_greylist_retry:
            return result

        # Save to MongoDB if enabled
        document_id = None
        if self.save_to_mongodb:
            try:
                document_id = utils.save_single_verification(email, verification_type, result, 0)
                result['mongodb_id'] = document_id
            except Exception as e:
                print(f"Error saving to MongoDB: {e}")

        # Start Celery retry task
        try:
            redis_connection_url = f"redis://{os.environ.get('FINDVERIFY_REDIS_HOST', 'localhost')}:{os.environ.get('FINDVERIFY_REDIS_PORT', '6379')}"

            if verification_type == 'verify':
                task = single_verify_task.apply_async(
                    args=[email, self.email_pool, redis_connection_url, wait_for_result, document_id, 0],
                    countdown=600  # 10 minutes
                )
            else:  # find
                # Parse email for find task
                parts = email.split('@')
                if len(parts) == 2:
                    local_part = parts[0]
                    domain = parts[1]
                    if '.' in local_part:
                        firstname, lastname = local_part.split('.', 1)
                    else:
                        firstname = local_part
                        lastname = ""

                    task = single_find_task.apply_async(
                        args=[firstname, lastname, domain, self.email_pool, redis_connection_url,
                             wait_for_result, document_id, 0],
                        countdown=600  # 10 minutes
                    )
                else:
                    raise ValueError(f"Invalid email format for find task: {email}")

            result['retry_task_id'] = task.id
            result['retry_scheduled'] = True
            result['next_retry_in'] = '10 minutes'

        except Exception as e:
            print(f"Error scheduling Celery retry: {e}")
            result['retry_error'] = str(e)

        return result

    def _save_result_to_mongodb(self, email: str, verification_type: str, result: Dict[str, Any]) -> str:
        """
        Save verification result to MongoDB
        :param email: Email address
        :param verification_type: 'verify' or 'find'
        :param result: Verification result
        :return: Document ID or None
        """
        if not self.save_to_mongodb:
            return None

        try:
            document_id = utils.save_single_verification(email, verification_type, result, 0)
            return document_id
        except Exception as e:
            print(f"Error saving to MongoDB: {e}")
            return None

    def verify_mail_async(self, email: str, wait_for_result: bool = True) -> Dict[str, Any]:
        """
        Verify email asynchronously using Celery
        :param email: Email address to verify
        :param wait_for_result: Whether to wait for result or return immediately
        :return: Verification result or task info
        """
        try:
            redis_connection_url = f"redis://{os.environ.get('FINDVERIFY_REDIS_HOST', 'localhost')}:{os.environ.get('FINDVERIFY_REDIS_PORT', '6379')}"

            if wait_for_result:
                # Execute synchronously and return result
                result = single_verify_task.apply(
                    args=[email, self.email_pool, redis_connection_url, True, None, 0]
                ).get()
                return result
            else:
                # Execute asynchronously and return task info
                task = single_verify_task.apply_async(
                    args=[email, self.email_pool, redis_connection_url, False, None, 0]
                )
                return {
                    'task_id': task.id,
                    'status': 'processing',
                    'message': 'Verification started',
                    'email': email
                }

        except Exception as e:
            return {
                'error': True,
                'message': f"Failed to start verification: {str(e)}",
                'email': email
            }

    def find_mail_async(self, firstname: str, lastname: str, domain: str,
                       wait_for_result: bool = True) -> Dict[str, Any]:
        """
        Find email asynchronously using Celery
        :param firstname: First name
        :param lastname: Last name
        :param domain: Domain
        :param wait_for_result: Whether to wait for result or return immediately
        :return: Find result or task info
        """
        try:
            redis_connection_url = f"redis://{os.environ.get('FINDVERIFY_REDIS_HOST', 'localhost')}:{os.environ.get('FINDVERIFY_REDIS_PORT', '6379')}"

            if wait_for_result:
                # Execute synchronously and return result
                result = single_find_task.apply(
                    args=[firstname, lastname, domain, self.email_pool, redis_connection_url, True, None, 0]
                ).get()
                return result
            else:
                # Execute asynchronously and return task info
                task = single_find_task.apply_async(
                    args=[firstname, lastname, domain, self.email_pool, redis_connection_url, False, None, 0]
                )
                email_identifier = f"{firstname}.{lastname}@{domain}" if lastname else f"{firstname}@{domain}"
                return {
                    'task_id': task.id,
                    'status': 'processing',
                    'message': 'Find operation started',
                    'email': email_identifier
                }

        except Exception as e:
            email_identifier = f"{firstname}.{lastname}@{domain}" if lastname else f"{firstname}@{domain}"
            return {
                'error': True,
                'message': f"Failed to start find operation: {str(e)}",
                'email': email_identifier
            }

    @staticmethod
    def _get_smtp_status_code(response: str) -> int:
        """
        Returns the SMTP status code for given response. If no response code was found, returns 0
        :param response: response from recipient mail server
        :return: SMTP response code; 0 if no SMTP code was found
        """
        # split into lines
        response_lines = response.split("\n")
        # remove empty strings from list
        response_lines = [line for line in response_lines if line.strip()]
        try:
            last_line: str = response_lines[-1]
        except IndexError:
            logger.error(f"smtp response was empty")
            return 0
        tokens = last_line.split(" ")
        try:
            status_code = int(tokens[0])
        except ValueError:
            logger.debug(f"Could not convert status code to number ({tokens[0]})")
            return 0

        return status_code

    def _get_mailserver_mx_record(self, domain: str) -> Optional[str]:
        """
        Fetches the highest priority MX record for this domain
        :param domain: ex. draftss.com
        :return: MX record (string) if found else None
        """
        # Get the MX_RECORD
        try:
            resolver_instance = resolver.Resolver()
            resolver_instance.timeout = 30.0
            resolver_instance.lifetime = 30.0
            resolver_answer = resolver_instance.resolve(domain, "MX")
            mail_exchangers = [exchange.to_text().split() for exchange in resolver_answer]
        except (resolver.NoAnswer, resolver.NXDOMAIN, resolver.NoNameservers, DNSTimeout) as err:
            logger.debug(f"{err}")
            return None

        if mail_exchangers:
            # Get the one with the highest priority
            return self._get_max_priority_mx_record(mail_exchangers)
        else:
            logger.debug("No Mail exchangers could be retrieved")
            return None

    @staticmethod
    def _get_max_priority_mx_record(mail_exchangers: list) -> str:
        """
        Returns MX record with lovest priority number (i.e. highest priority)
        :param mail_exchangers: list of mx records
        :return:
        """
        max_priority_mx_record = (999, 'stub')
        for record in mail_exchangers:
            if int(record[0]) < max_priority_mx_record[0]:
                max_priority_mx_record = (int(record[0]), record[1])
        return max_priority_mx_record[1]

    def _get_all_mx_records(self, domain: str) -> List[str]:
        """
        Fetches all MX records for this domain
        :param domain: ex. draftss.com
        :return: List of MX records
        """
        try:
            resolver_instance = resolver.Resolver()
            resolver_instance.timeout = 30.0
            resolver_instance.lifetime = 30.0
            resolver_answer = resolver_instance.resolve(domain, "MX")
            mail_exchangers = [exchange.to_text().split() for exchange in resolver_answer]
            return [record[1] for record in mail_exchangers]
        except (resolver.NoAnswer, resolver.NXDOMAIN, resolver.NoNameservers, DNSTimeout):
            return []

    def _connect_to_server(self, mail_server: str, mail_server_port: int) -> Optional[socket.socket]:
        """
        Creates socket connection to provided mail server & port
        :param mail_server: MX record of a domain (ex. aspmx.l.google.com)
        :param mail_server_port: Mail server communication port. Usually this is port 25.
        :return: socket connection object if successfuly created (response status is 220 or 250); else None
        """
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        # connect
        try:
            s.connect((mail_server, mail_server_port))
        except (timeout, ConnectionRefusedError):
            logger.debug("socket connection timed out")
            return None
        except socket.gaierror as err:
            logger.debug(f"{err}")
            return None

        resp = s.recv(self.SOCKET_RESP_BUFFER_SIZE)
        resp = resp.decode()

        # stop proceeding if "bnshosting" or "octopusdns" is in resp
        if "bnshosting" in resp or "octopusdns" in resp:
            s.close()
            return None

        smtp_status_code = MailVerifier._get_smtp_status_code(resp)
        if smtp_status_code in [220, 250]:
            return s
        else:
            return None

    def _send__ehlo(self, our_domain: str, socket_connection: socket.socket) -> tuple[int, str]:
        """
        Makes EHLO command to the target mail server. Returns SMTP status code and response.
        :param our_domain: our mailserver domain
        :param socket_connection: socket connection object
        :return: tuple of (status code, full response)
        :raises: ConnectionResetError
        """
        socket_connection.send(f"EHLO {our_domain}\r\n".encode())
        resp = socket_connection.recv(self.SOCKET_RESP_BUFFER_SIZE)
        resp = resp.decode()
        logger.debug(resp)
        return self._get_smtp_status_code(resp), resp

    def _send__mail_from(self, our_email_id: str, socket_connection: socket.socket) -> tuple[int, str]:
        """
        Makes MAIL FROM command to the target mail server. Returns SMTP status code and response.
        :param our_email_id: email id we are using for communication with target mail server.
        This will be used in MAIL FROM command
        :param socket_connection: socket connection object
        :return: tuple of (status code, full response)
        :raises: ConnectionResetError
        """
        socket_connection.send(f"MAIL FROM:<{our_email_id}>\r\n".encode())
        resp = socket_connection.recv(self.SOCKET_RESP_BUFFER_SIZE)
        resp = resp.decode()
        logger.debug(resp)
        return self._get_smtp_status_code(resp), resp

    def _send__rcpt_to(self, recipient_email_id: str, socket_connection: socket.socket) -> tuple[int, str]:
        """
        Makes RCPT TO command to the target mail server. Returns SMTP status code and response.
        :param recipient_email_id: email id to verify. This is used in RCPT TO command.
        :param socket_connection: socket connection object
        :return: tuple of (status code, full response)
        :raises: ConnectionResetError
        """
        socket_connection.send(f"RCPT TO:<{recipient_email_id}>\r\n".encode())
        resp = socket_connection.recv(self.SOCKET_RESP_BUFFER_SIZE)
        resp = resp.decode()
        logger.debug(resp)
        return self._get_smtp_status_code(resp), resp

    def _send__quit_command(self, socket_connection: socket.socket) -> int:
        """
        Makes QUIT command to the target mail server. Returns SMTP status code. This will close the communications
        but not the actual socket connection itself. That needs to be closed manually or using with...as block
        :param socket_connection: socket connection object
        :return: status code
        :raises: ConnectionResetError
        """
        socket_connection.send(f"QUIT\r\n".encode())
        resp = socket_connection.recv(self.SOCKET_RESP_BUFFER_SIZE)
        resp = resp.decode()
        logger.debug(resp)
        return self._get_smtp_status_code(resp)

    def verify_mail(self, email: str):
        """
        Verifies the provided Email ID. Returns lookup dictionary data.
        :param email: The Email ID to verify
        :type email: str
        """

        our_email_id: str = random.choice(self.email_pool)
        logger.debug(f"using {our_email_id}")
        our_domain: str = our_email_id.split("@")[-1]

        recipient_email_id = email.lstrip().rstrip()
        recipient_domain = email.split("@")[-1]

        lookup = {
            'address': Address(email, recipient_domain),
            'valid_format': False,  # Email syntax is correct
            'deliverable': False,  # We can send messages to this email id
            'full_inbox': False,  # Their inbox is full
            'host_exists': True,  # The domain (ex. draftss.com) is a valid and existing domain
            'catch_all': False,  # Their mail servers use catch all
            'disposable': False,  # This is a disposable email id provided by such services
            'connection_error': False,  # Unable to connect to their SMTP server
            'greylist': False,  # This email / domain triggered a greylist code / string
            'dangerous': False,  # This mail server might get us blacklisted / greylisted or cause some other issues
            'message': "You can't send messages to this Email ID",  # Message for the user
            'debug_message': "Nothing to report",  # Message for us
            'mx_records': [],  # MX records for the domain
            'esp': 'unknown',  # Email Service Provider
            'final_smtp_code': None,  # Final SMTP response code
            'final_smtp_message': '',  # Final SMTP response message
            'smtp_handshake_log': []  # Full SMTP handshake log
        }

        # ************************************************************************************
        # =============================== DECONTAMINATION ZONE ===============================
        # ************************************************************************************

        # Check if this domain has been marked in redis (greylist causing domains)
        if self.redis_greylist_instance and self.redis_greylist_instance.get(recipient_domain):
            print(f"{recipient_domain} is marked in redis greylist")
            lookup['dangerous'] = True
            lookup['message'] = "Couldn't perform requested operation. Server busy."
            lookup['debug_message'] = f"{recipient_domain} is marked in Redis for greylisting."
            return lookup

        # Validate email
        if not self._email_format_is_valid(email):
            lookup['message'] = "Not a valid Email ID format"
            lookup['message'] = f"{email} is not a valid Email ID format"
            return lookup
        else:
            lookup['valid_format'] = True

        # ************************************************************************************
        # ====================================================================================
        # ************************************************************************************

        try:
            # Get MX record and all MX records for enhanced data
            mx_record = self._get_mailserver_mx_record(recipient_domain)
            all_mx_records = self._get_all_mx_records(recipient_domain)
            lookup['mx_records'] = all_mx_records

            if not mx_record:
                lookup.update({
                    'valid_format': True,
                    'deliverable': False,
                    'full_inbox': False,
                    'host_exists': False,
                    'catch_all': False,
                    'disposable': False,
                    'connection_error': True,
                    'greylist': False,
                    'dangerous': False,
                    'message': "We could not find any mail server for this domain",
                    'debug_message': f"No MX record could be found for {recipient_domain}"
                })
                return lookup

            # Detect ESP
            esp = self._detect_esp(mx_record)
            lookup['esp'] = esp

            # Create connection
            s = self._connect_to_server(mx_record, 25)
            if not s:
                logger.debug(f"Could not connect to {mx_record}:{25} for {email}")
                return {
                    'address': Address(email, recipient_domain),
                    'valid_format': True,
                    'deliverable': False,
                    'full_inbox': False,
                    'host_exists': False,
                    'catch_all': False,
                    'disposable': False,
                    'connection_error': True,
                    'greylist': False,
                    'dangerous': False,
                    'message': f"We could not connect to {recipient_domain}'s mail server",
                    'debug_message': f"Could not connect to {mx_record}:{25} ({email})"
                }

            # Run EHLO command
            try:
                ehlo_status, ehlo_response = self._send__ehlo(our_domain, s)
                lookup['smtp_handshake_log'].append(f"EHLO {our_domain} -> {ehlo_status}: {ehlo_response.strip()}")
            except (ConnectionResetError, BrokenPipeError):
                return handle_connection_error(lookup)
            except Exception as err:
                logger.error(f"Error during EHLO: {err}")
                lookup['smtp_handshake_log'].append(f"EHLO {our_domain} -> ERROR: {err}")
                s.close()
                return lookup

            # Run MAIL FROM command
            try:
                mail_from_status, mail_from_response = self._send__mail_from(our_email_id, s)
                lookup['smtp_handshake_log'].append(f"MAIL FROM:<{our_email_id}> -> {mail_from_status}: {mail_from_response.strip()}")
            except (ConnectionResetError, BrokenPipeError):
                return handle_connection_error(lookup)
            except Exception as err:
                logger.error(f"Error during MAIL FROM: {err}")
                lookup['smtp_handshake_log'].append(f"MAIL FROM:<{our_email_id}> -> ERROR: {err}")
                s.close()
                return lookup

            # Run RCPT TO command
            # NOTE: 'Not Deliverable' case is automatically handled since its default value is False
            try:
                rcpt_status, rcpt_response = self._send__rcpt_to(recipient_email_id, s)
                lookup['smtp_handshake_log'].append(f"RCPT TO:<{recipient_email_id}> -> {rcpt_status}: {rcpt_response.strip()}")
                lookup['final_smtp_code'] = rcpt_status
                lookup['final_smtp_message'] = rcpt_response.strip()
            except Exception as err:
                logger.error(f"Error during RCPT TO: {err}")
                lookup['smtp_handshake_log'].append(f"RCPT TO:<{recipient_email_id}> -> ERROR: {err}")
                return lookup

            # Get ESP-specific success codes
            esp_success_codes = self.rcpt_success_codes.get(esp, self.rcpt_success_codes['others'])

            # Check if rcpt response code matches one of the greylist_codes. In that case we need to stop the execution.
            if rcpt_status in self.greylist_codes:
                lookup["greylist"] = True
                lookup['message'] = "Couldn't perform requested operation. Server busy."
                lookup['debug_message'] = f"{recipient_domain} is marked in Redis for greylisting."

                # Handle greylist with retry system
                lookup = self._handle_greylist_response(email, 'verify', lookup)

                # Still save to Redis for immediate blocking
                if self.redis_greylist_instance:
                    self.redis_greylist_instance.set(recipient_domain, recipient_domain)
                    self.redis_greylist_instance.expire(recipient_domain, self.redis_greylist_record_expiry)
                    print(f"greylist code caught for email id {email}")

            elif rcpt_status in esp_success_codes:
                # Deliverable if gibberish rcpt check in next step doesn't return status code 250
                lookup['deliverable'] = True
                lookup['message'] = "You can send messages to this Email ID"
                lookup['debug_message'] = f"first rcpt() returned status code {rcpt_status}"
                # RCPT TO - gibberish email
                try:
                    gibberish_email_rcpt_status, gibberish_response = self._send__rcpt_to(self._generate_random_email(recipient_domain), s)
                    lookup['smtp_handshake_log'].append(f"RCPT TO:<gibberish> -> {gibberish_email_rcpt_status}: {gibberish_response.strip()}")

                    # Only consider 250 code for catch-all detection to avoid greylisting issues
                    if gibberish_email_rcpt_status == 250:
                        # Catch All case
                        lookup['catch_all'] = True
                        lookup['message'] = "This domain uses a catch-all mailbox"
                        lookup['debug_message'] = f"catch_all rcpt() returned status code {gibberish_email_rcpt_status}"
                except Exception as err:
                    logger.error(f"Error during gibberish RCPT TO: {err}")
                    lookup['smtp_handshake_log'].append(f"RCPT TO:<gibberish> -> ERROR: {err}")
                    lookup['catch_all'] = True
                    lookup['message'] = "This domain uses a catch-all mailbox"
                    lookup['debug_message'] = f"gibberish RCPT ran into some error"

            elif rcpt_status >= 400:
                lookup['debug_message'] = f"rcpt() returned status code {rcpt_status}"
                raise SMTPRecipientException(code=rcpt_status, response="RCPT failed on first try")

            # run QUIT command and close the connection
            try:
                self._send__quit_command(s)
            except (ConnectionResetError, BrokenPipeError):
                # connection was already closed. don't do anything in this case
                pass
            except Exception as err:
                logger.error(f"Error during QUIT command: {err}")

            s.close()

        except SMTPRecipientException as err:
            kwargs = other_errors.get(err.code, handle_unrecognized_error)(err.response)
            # Merge with lookup dictionary
            lookup = {**lookup, **kwargs}
            if 's' in locals():
                # run QUIT command and close connection
                try:
                    # noinspection PyUnboundLocalVariable
                    self._send__quit_command(s)
                except (ConnectionResetError, BrokenPipeError):
                    return handle_connection_error(lookup)
                except Exception as err:
                    logger.error(f"Error during QUIT command: {err}")
                s.close()

        except smtplib.SMTPServerDisconnected as err:
            # Might be blocked by Spamhaus or other such services
            lookup['message'] = "Connection closed unexpectedly by services"
            lookup['debug_message'] = f"rcpt() or mail() went into SMTPServerDisconnected exception\n" \
                                      f"Error is as follows: {err}"
            if 's' in locals():
                # run QUIT command and close connection
                try:
                    # noinspection PyUnboundLocalVariable
                    self._send__quit_command(s)
                except (ConnectionResetError, BrokenPipeError):
                    return handle_connection_error(lookup)
                except Exception as err:
                    logger.error(f"Error during QUIT command: {err}")
                s.close()

        except smtplib.SMTPConnectError as err:
            # Connection Refused. You might be blacklisted
            lookup['message'] = "Connection closed unexpectedly"
            lookup['debug_message'] = f"rcpt() or mail() went into SMTPConnectError\n" \
                                      f"Error is as follows {err}"
            if 's' in locals():
                # run QUIT command and close connection
                try:
                    # noinspection PyUnboundLocalVariable
                    self._send__quit_command(s)
                except (ConnectionResetError, BrokenPipeError):
                    return handle_connection_error(lookup)
                except Exception as err:
                    logger.error(f"Error during QUIT command: {err}")
                s.close()

        # Save successful verification to MongoDB (if not greylist)
        if not lookup.get('greylist', False):
            mongodb_id = self._save_result_to_mongodb(email, 'verify', lookup)
            if mongodb_id:
                lookup['mongodb_id'] = mongodb_id

        return lookup

    def find_mail(self, firstname: str, lastname: str, recipient_domain: str) -> Dict:
        """
        Finds email id using provided user details
        :param firstname: ex. amin
        :param lastname: ex. memon
        :param recipient_domain: ex. draftss.com
        :return: result dictionary
        """

        # Default results
        lookup = {
            'address': Address(None, recipient_domain),
            'user_details': [firstname, lastname, recipient_domain],
            'valid_format': True,  # Email syntax is correct
            'deliverable': False,  # We can send messages to this email id
            'full_inbox': False,  # Their inbox is full
            'host_exists': True,  # The domain (ex. draftss.com) is a valid and existing domain
            'catch_all': False,  # Their mail servers use catch all
            'disposable': False,  # This is a disposable email id provided by such services
            'connection_error': False,  # Unable to connect to their SMTP server
            'greylist': False,  # This email / domain triggered a greylist code / string
            'dangerous': False,  # This mail server might get us blacklisted / greylisted or cause some other issues
            'message': f"No emails found for this person",  # Message for the user
            'debug_message': "Nothing to report",  # Message for us
            'mx_records': [],  # MX records for the domain
            'esp': 'unknown',  # Email Service Provider
            'final_smtp_code': None,  # Final SMTP response code
            'final_smtp_message': '',  # Final SMTP response message
            'smtp_handshake_log': []  # Full SMTP handshake log
        }

        our_email_id: str = random.choice(self.email_pool)
        our_domain: str = our_email_id.split("@")[-1]

        # Clean the names
        firstname = firstname.lower().lstrip().rstrip()
        lastname = lastname.lower().lstrip().rstrip()
        recipient_domain = recipient_domain.lower().lstrip().rstrip()

        # Check if this domain has been marked in redis (greylist causing domains)
        if self.redis_greylist_instance and self.redis_greylist_instance.get(recipient_domain):
            print(f"{recipient_domain} is marked in redis greylist")
            lookup['dangerous'] = True
            lookup['message'] = "Couldn't perform requested operation. Server busy."
            lookup['debug_message'] = f"{recipient_domain} is marked in Redis for greylisting."
            return lookup

        # Get MX record and all MX records for enhanced data
        mx_record = self._get_mailserver_mx_record(recipient_domain)
        all_mx_records = self._get_all_mx_records(recipient_domain)
        lookup['mx_records'] = all_mx_records

        if not mx_record:
            lookup['debug_message'] = f"No MX record found for {recipient_domain}"
            return lookup

        # Detect ESP
        esp = self._detect_esp(mx_record)
        lookup['esp'] = esp

        # Get email combinations
        email_list = self._generate_emails_from_name(firstname, lastname, recipient_domain)

        # =============================================================================================
        # ---------------------------------------------------------------------------------------------
        # =============================================================================================

        # Create connection
        s = self._connect_to_server(mx_record, 25)
        if not s:
            lookup['debug_message'] = f"Could not connect with {mx_record}"
            return lookup

        # Run EHLO command
        try:
            ehlo_status, ehlo_response = self._send__ehlo(our_domain, s)
            lookup['smtp_handshake_log'].append(f"EHLO {our_domain} -> {ehlo_status}: {ehlo_response.strip()}")
        except (ConnectionResetError, BrokenPipeError):
            return handle_connection_error(lookup)

        # Run MAIL FROM command
        try:
            mail_from_status, mail_from_response = self._send__mail_from(our_email_id, s)
            lookup['smtp_handshake_log'].append(f"MAIL FROM:<{our_email_id}> -> {mail_from_status}: {mail_from_response.strip()}")
        except (ConnectionResetError, BrokenPipeError):
            return handle_connection_error(lookup)

        # Detect ESP for this domain
        esp = self._detect_esp(mx_record)
        esp_success_codes = self.rcpt_success_codes.get(esp, self.rcpt_success_codes['others'])

        try:
            # For each email try sending RCPT command
            for email in email_list:
                # Run RCPT TO command
                # NOTE: 'Not Deliverable' case is automatically handled since its default value is False
                rcpt_status, rcpt_response = self._send__rcpt_to(email, s)
                lookup['smtp_handshake_log'].append(f"RCPT TO:<{email}> -> {rcpt_status}: {rcpt_response.strip()}")
                lookup['final_smtp_code'] = rcpt_status
                lookup['final_smtp_message'] = rcpt_response.strip()

                # Check if rcpt response code matches one of the greylist_codes.
                # In that case we need to stop the execution.
                if rcpt_status in self.greylist_codes:
                    lookup["greylist"] = True
                    lookup['message'] = "Couldn't perform requested operation. Server busy."
                    lookup['debug_message'] = f"{recipient_domain} is marked in Redis for greylisting."

                    # Handle greylist with retry system
                    email_for_retry = f"{firstname}.{lastname}@{recipient_domain}" if lastname else f"{firstname}@{recipient_domain}"
                    lookup = self._handle_greylist_response(email_for_retry, 'find', lookup)

                    # Still save to Redis for immediate blocking
                    if self.redis_greylist_instance:
                        self.redis_greylist_instance.set(recipient_domain, recipient_domain)
                        self.redis_greylist_instance.expire(recipient_domain, self.redis_greylist_record_expiry)
                        print(f"greylist code caught for email id {email_for_retry}")

                elif rcpt_status in esp_success_codes:
                    # RCPT TO - gibberish email
                    gibberish_email_rcpt_status, gibberish_response = self._send__rcpt_to(
                        self._generate_random_email(recipient_domain), s)
                    lookup['smtp_handshake_log'].append(f"RCPT TO:<gibberish> -> {gibberish_email_rcpt_status}: {gibberish_response.strip()}")

                    # Only consider 250 code for catch-all detection to avoid greylisting issues
                    if gibberish_email_rcpt_status == 250:
                        # Email is catch-all. No need to look further
                        lookup['address'] = Address(None, recipient_domain)
                        lookup['deliverable'] = True
                        lookup['catch_all'] = True
                        lookup['message'] = "This recipient_domain uses a catch-all mailbox"
                        lookup['debug_message'] = f"second rcpt check failed" \
                                                  f" with status code {gibberish_email_rcpt_status}"
                        # run QUIT command and close the connection
                        try:
                            self._send__quit_command(s)
                        except (ConnectionResetError, BrokenPipeError):
                            return handle_connection_error(lookup)
                        except Exception as err:
                            logger.error(f"Error during QUIT command: {err}")
                        s.close()
                        return lookup
                    else:
                        # Email is deliverable. We found it!
                        lookup['address'] = Address(email, recipient_domain)
                        lookup['deliverable'] = True
                        lookup['message'] = "You can send messages to this Email ID"
                        # run QUIT command and close the connection
                        try:
                            self._send__quit_command(s)
                        except (ConnectionResetError, BrokenPipeError):
                            return handle_connection_error(lookup)
                        except Exception as err:
                            logger.error(f"Error during QUIT command: {err}")
                        s.close()
                        return lookup

                # Reset SMTP state for next RCPT
                # self._reset_smtp_state_to_before_rcpt(smtp_instance, our_email_id)

        except Exception as err:
            logger.error(err)
            lookup['debug_message'] = f"Error occured while running RCPT: {err}"
            # run QUIT command and close the connection
            try:
                self._send__quit_command(s)
            except (ConnectionResetError, BrokenPipeError):
                return handle_connection_error(lookup)
            except Exception as err:
                logger.error(f"Error during QUIT command: {err}")
            s.close()
            return lookup

        # run QUIT command and close the connection
        try:
            self._send__quit_command(s)
        except (ConnectionResetError, BrokenPipeError):
            # Don't do anything
            pass
        except Exception as err:
            logger.error(f"Error during QUIT command: {err}")
        s.close()

        # Save result to MongoDB (if not greylist)
        if not lookup.get('greylist', False):
            email_for_save = f"{firstname}.{lastname}@{recipient_domain}" if lastname else f"{firstname}@{recipient_domain}"
            mongodb_id = self._save_result_to_mongodb(email_for_save, 'find', lookup)
            if mongodb_id:
                lookup['mongodb_id'] = mongodb_id

        return lookup

    def bulk_verify_mail(self, emails: List[str]) -> Dict[str, Any]:
        """
        Bulk verify emails with greylist retry support
        :param emails: List of email addresses to verify
        :return: Bulk verification result with status and batch_id
        """
        batch_id = str(uuid.uuid4())

        # Create bulk verification record in MongoDB
        if self.save_to_mongodb and self.db:
            try:
                self.db.create_bulk_verification(batch_id, 'verify', len(emails))
            except Exception as e:
                print(f"Error creating bulk verification record: {e}")

        # Start Celery task for bulk processing
        try:
            task = bulk_verification_with_retries.apply_async(
                args=[batch_id, emails, 'verify']
            )

            return {
                'batch_id': batch_id,
                'status': 'inprogress',
                'total_emails': len(emails),
                'processed_emails': 0,
                'task_id': task.id,
                'message': 'Bulk verification started',
                'results': []
            }

        except Exception as e:
            return {
                'batch_id': batch_id,
                'status': 'error',
                'error': str(e),
                'message': 'Failed to start bulk verification'
            }

    def bulk_find_mail(self, user_data: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Bulk find emails with greylist retry support
        :param user_data: List of dicts with firstname, lastname, domain
        :return: Bulk find result with status and batch_id
        """
        batch_id = str(uuid.uuid4())

        # Create bulk verification record in MongoDB
        if self.save_to_mongodb and self.db:
            try:
                self.db.create_bulk_verification(batch_id, 'find', len(user_data))
            except Exception as e:
                print(f"Error creating bulk find record: {e}")

        # Start Celery task for bulk processing
        try:

            task = bulk_verification_with_retries.apply_async(
                args=[batch_id, user_data, 'find']
            )

            return {
                'batch_id': batch_id,
                'status': 'inprogress',
                'total_emails': len(user_data),
                'processed_emails': 0,
                'task_id': task.id,
                'message': 'Bulk find started',
                'results': []
            }

        except Exception as e:
            return {
                'batch_id': batch_id,
                'status': 'error',
                'error': str(e),
                'message': 'Failed to start bulk find'
            }

    def get_bulk_status(self, batch_id: str) -> Dict[str, Any]:
        """
        Get status of bulk verification/find operation
        :param batch_id: Batch identifier
        :return: Current status and results
        """
        if not self.save_to_mongodb or not self.db:
            return {
                'error': 'MongoDB not available',
                'message': 'Cannot retrieve bulk status without MongoDB'
            }

        try:
            bulk_data = self.db.get_bulk_verification(batch_id)
            if not bulk_data:
                return {
                    'error': 'Batch not found',
                    'message': f'No batch found with ID: {batch_id}'
                }

            return {
                'batch_id': bulk_data['batch_id'],
                'status': bulk_data['status'],
                'verification_type': bulk_data['verification_type'],
                'total_emails': bulk_data['total_emails'],
                'processed_emails': bulk_data['processed_emails'],
                'results': bulk_data.get('results', []),
                'created_at': bulk_data['created_at'],
                'updated_at': bulk_data['updated_at'],
                'completed_at': bulk_data.get('completed_at')
            }

        except Exception as e:
            return {
                'error': str(e),
                'message': 'Error retrieving bulk status'
            }

    @staticmethod
    def _generate_emails_from_name(fn, ln, dn) -> List:
        """
        Generates email ids using firstname, lastname and domain values
        :param fn: first name
        :param ln: last name
        :param dn: domain

        :return: List of email ids
        """
        if fn and ln:
            combinations = {
                'c0': lambda _fn, _ln, _dn: f"{_fn + _ln}@{_dn}",
                'c1': lambda _fn, _ln, _dn: f"{_ln + _fn}@{_dn}",
                'c2': lambda _fn, _ln, _dn: f"{_fn[0] + _ln}@{_dn}",
                'c3': lambda _fn, _ln, _dn: f"{_ln[0] + _fn}@{_dn}",
                'c4': lambda _fn, _ln, _dn: f"{_fn}@{_dn}",
                'c5': lambda _fn, _ln, _dn: f"{_ln}@{_dn}",
                'c6': lambda _fn, _ln, _dn: f"{_fn + '.' + _ln}@{_dn}",
                'c7': lambda _fn, _ln, _dn: f"{_ln + '.' + _fn}@{_dn}",
            }
        elif not fn:
            combinations = {
                'c0': lambda _fn, _ln, _dn: f"{_ln}@{_dn}",
            }

        else:
            combinations = {
                'c0': lambda _fn, _ln, _dn: f"{_fn}@{_dn}",
            }

        email_list = [combinations[f"c{i}"](fn, ln, dn) for i in range(len(combinations.keys()))]

        return email_list

    @staticmethod
    def _reset_smtp_state_to_before_rcpt(smtp_instance: SMTP, our_email_id: str):
        """
        This function reset SMTP connection to its original state and runs HELO & MAIL FROM commands.
        Some mail servers return (452 4.5.3 Too many recipients) response. Resetting SMTP state helps to work around
        this issue.
        :param smtp_instance: SMTP object
        :return: void
        """
        # Reset connection to original state
        smtp_instance.rset()
        smtp_instance.helo(name='liverify2.prospectss.com')
        smtp_instance.mail(our_email_id)

    def _get_success_codes_list(self, mx_record_domain: str) -> List[int]:
        """
        Returns list of RCPT codes that we are assuming to be success responses
        :param mx_record_domain: Fetch this from MX record (ex. google.com)
        :return: List of success codes (int)
        """
        if mx_record_domain in self.rcpt_success_codes:
            return self.rcpt_success_codes[mx_record_domain]
        else:
            return self.rcpt_success_codes['others']

    @staticmethod
    def _generate_random_email(domain: str) -> str:
        """
        Generates gibberish email id using provided domain
        :param domain: ex. draftss.com
        :return: gibberish email
        """
        name = binascii.hexlify(os.urandom(20)).decode()[:5]
        return f"{name}@{domain}"

    @staticmethod
    def _email_format_is_valid(email_id) -> bool:
        """
        Regex based email id format validation
        :param email_id: ex. <EMAIL>
        """
        email_regex = r"(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)"
        if re.match(email_regex, email_id):  # re.match() returns a Match object
            return True
        else:
            return False
