"""
Utility functions for email verification, MongoDB operations, and greylist retry logic
"""

import os
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Tuple

from redis import Redis
from bson import ObjectId
from pymongo import MongoClient

from verifier import MailVerifier

logger = logging.getLogger(__name__)


def get_mongo_connection():
    """Get MongoDB connection"""
    mongo_host = os.environ.get('FINDVERIFY_MONGO_HOST', 'localhost')
    mongo_port = int(os.environ.get('FINDVERIFY_MONGO_PORT', '27017'))
    return MongoClient(mongo_host, mongo_port)


def get_mongo_collections():
    """Get MongoDB collections for email verification"""
    client = get_mongo_connection()
    db = client["findverify_db"]
    return {
        'single_verifications': db["single_verifications"],
        'bulk_verify_tasks': db["bulk_verify_tasks"],
        'bulk_find_tasks': db["bulk_find_tasks"]
    }


def save_single_verification(email: str, verification_type: str, result: Dict[str, Any],
                             greylist_retry_count: int = 0) -> str:
    """
    Save single email verification result to MongoDB
    :param email: Email address
    :param verification_type: 'verify' or 'find'
    :param result: Verification result dictionary
    :param greylist_retry_count: Number of greylist retries
    :return: Document ID
    """
    try:
        collections = get_mongo_collections()

        document = {
            'email': email,
            'verification_type': verification_type,
            'result': result,
            'greylist_retry_count': greylist_retry_count,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow(),
            'is_greylist_retry': greylist_retry_count > 0,
            'final_result': greylist_retry_count >= 3
        }

        result_doc = collections['single_verifications'].insert_one(document)
        return str(result_doc.inserted_id)

    except Exception as e:
        logger.error(f"Error saving single verification: {e}")
        return None


def update_single_verification(document_id: str, result: Dict[str, Any],
                               greylist_retry_count: int) -> bool:
    """
    Update single verification with retry result
    :param document_id: MongoDB document ID
    :param result: Updated verification result
    :param greylist_retry_count: Updated retry count
    :return: Success status
    """
    try:
        collections = get_mongo_collections()

        update_data = {
            '$set': {
                'result': result,
                'greylist_retry_count': greylist_retry_count,
                'updated_at': datetime.utcnow(),
                'final_result': greylist_retry_count >= 3
            }
        }

        result_doc = collections['single_verifications'].update_one(
            {'_id': ObjectId(document_id)},
            update_data
        )
        return result_doc.modified_count > 0

    except Exception as e:
        logger.error(f"Error updating single verification: {e}")
        return False


def should_retry_greylist(result: Dict[str, Any]) -> bool:
    """
    Check if result indicates greylist and should be retried
    :param result: Verification result
    :return: True if should retry
    """
    return result.get('greylist', False) and not result.get('final_result', False)


def get_retry_delay(retry_count: int) -> int:
    """
    Get retry delay in seconds based on retry count
    :param retry_count: Current retry count (0, 1, 2)
    :return: Delay in seconds
    """
    delays = [600, 1200, 1800]  # 10m, 20m, 30m
    if retry_count < len(delays):
        return delays[retry_count]
    return delays[-1]  # Use last delay if beyond array


def mark_as_catch_all_after_retries(result: Dict[str, Any], retry_count: int) -> Dict[str, Any]:
    """
    Mark email as catch-all after max retries
    :param result: Current verification result
    :param retry_count: Number of retries attempted
    :return: Updated result
    """
    if retry_count >= 3:
        result['catch_all'] = True
        result['greylist'] = False
        result['final_result'] = True
        result['message'] = "Marked as catch-all after 3 greylist retries"
        result['debug_message'] = f"Max greylist retries ({retry_count}) reached"

    return result


def process_bulk_with_retries(emails_or_userdata: List, verification_type: str,
                              from_emails_list: List[str], redis_instance: Redis,
                              task_uid: str) -> List[Tuple]:
    """
    Process bulk verification/find with greylist retries
    :param emails_or_userdata: List of emails or user data
    :param verification_type: 'verify' or 'find'
    :param from_emails_list: From email addresses
    :param redis_instance: Redis instance
    :param task_uid: Task identifier
    :return: List of results
    """
    verifier = MailVerifier(from_emails_list, redis_instance)
    results = []
    retry_queue = {}  # {email: (data, retry_count)}

    # Progressive delays: 10m, 20m, 30m
    delays = [600, 1200, 1800]

    def process_item(item, retry_count=0):
        """Process a single item (email or user data)"""
        try:
            if verification_type == 'verify':
                email = item
                result = verifier.verify_mail(email)
                identifier = email
            else:  # find
                if isinstance(item, list):
                    fname, lname, domain = item[0], item[1], item[2]
                else:
                    fname, lname, domain = item['firstname'], item['lastname'], item['domain']

                result = verifier.find_mail(fname, lname, domain)
                identifier = f"{fname}.{lname}@{domain}" if lname else f"{fname}@{domain}"

            # Add retry information
            result['retry_count'] = retry_count
            result['task_uid'] = task_uid

            return identifier, result, item

        except Exception as e:
            logger.error(f"Error processing item {item}: {e}")
            error_result = {
                'error': True,
                'message': f"Processing error: {str(e)}",
                'retry_count': retry_count,
                'task_uid': task_uid
            }
            identifier = str(item)
            return identifier, error_result, item

    # Initial processing
    for item in emails_or_userdata:
        identifier, result, original_item = process_item(item)

        if should_retry_greylist(result):
            retry_queue[identifier] = (original_item, 0)

        results.append((identifier, result))

    # Process retries with delays
    for delay_index, delay in enumerate(delays):
        if not retry_queue:
            break

        logger.info(f"Waiting {delay}s before retry {delay_index + 1} for {len(retry_queue)} items")
        time.sleep(delay)

        current_retry_queue = retry_queue.copy()
        retry_queue.clear()

        for identifier, (original_item, current_retry_count) in current_retry_queue.items():
            new_identifier, result, _ = process_item(original_item, current_retry_count + 1)

            if should_retry_greylist(result) and current_retry_count + 1 < 3:
                retry_queue[identifier] = (original_item, current_retry_count + 1)
            elif should_retry_greylist(result) and current_retry_count + 1 >= 3:
                result = mark_as_catch_all_after_retries(result, current_retry_count + 1)

            # Update result in results list
            for i, (res_id, res_data) in enumerate(results):
                if res_id == identifier:
                    results[i] = (identifier, result)
                    break

    return results
