services:
  # FastAPI python webserver
  fastapi:
    image: python:3.11
    ports:
      - 8000:8000
    volumes:
      - ".:/app"
      - "/app/.venv"  # ignore .venv folder
    environment:
      - FINDVERIFY_VERIFIER_EMAILS=<EMAIL>
      - FINDVERIFY_REDIS_HOST=redis
      - FINDVERIFY_REDIS_PORT=6379
      - FINDVERIFY_MONGO_HOST=mongodb
      - FINDVERIFY_MONGO_PORT=27017
    working_dir: /app
    command: bash -c "pip install virtualenv && 
      python -m virtualenv .venv && 
      source .venv/bin/activate && 
      pip install -r requirements.txt && 
      python -m uvicorn --host 0.0.0.0 main:app --reload"
    depends_on:
      - redis
      - mongodb
    networks:
      - findverify

  celery-worker:
    image: python:3.11
    volumes:
      - ".:/app"
      - "/app/.venv"  # ignore .venv folder
    environment:
      - FINDVERIFY_VERIFIER_EMAILS=<EMAIL>
      - FINDVERIFY_REDIS_HOST=redis
      - FINDVERIFY_REDIS_PORT=6379
      - FINDVERIFY_MONGO_HOST=mongodb
      - FINDVERIFY_MONGO_PORT=27017
    working_dir: /app
    command: bash -c "pip install virtualenv &&
      python -m virtualenv .venv &&
      source .venv/bin/activate &&
      pip install -r requirements.txt &&
      celery -A worker.celery worker --loglevel=info"
    depends_on:
      - redis
      - mongodb
    networks:
      - findverify

  # Redis for Greylist
  redis:
    image: redis:latest
    networks:
      - findverify

  # MongoDB to store results.
  mongodb:
    image: mongo:8.0-rc
    volumes:
      - ./mongodb_data:/data/db
    ports:
      - 4080:27017
    networks:
      - findverify

# Network for inter-container communication.
networks:
  findverify:
    driver: bridge
