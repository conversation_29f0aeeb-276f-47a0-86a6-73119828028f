"""
Celery configuration and tasks for greylist retry handling
"""

from celery import Celery
from datetime import datetime, timedelta
import os
import time
from typing import Dict, Any

# Celery configuration
celery_app = Celery(
    'email_verifier',
    broker=os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0'),
    backend=os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0'),
    include=['celery_app']
)

# Celery settings
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    task_acks_late=True,
)


@celery_app.task(bind=True, max_retries=3)
def retry_greylist_verification(self, email: str, verification_type: str, 
                               document_id: str, retry_count: int = 0):
    """
    Celery task to retry greylist verification with progressive delays
    :param email: Email to verify
    :param verification_type: 'verify' or 'find'
    :param document_id: MongoDB document ID
    :param retry_count: Current retry count (0, 1, 2)
    """
    from verifier import MailVerifier
    from models import db
    
    # Progressive delays: 10m, 20m, 30m
    delays = [600, 1200, 1800]  # 10, 20, 30 minutes in seconds
    
    try:
        print(f"Retrying greylist verification for {email}, attempt {retry_count + 1}")
        
        # Initialize verifier
        verifier = MailVerifier(['<EMAIL>'])  # Use appropriate sender
        
        # Perform verification
        if verification_type == 'verify':
            result = verifier.verify_mail(email)
        else:  # find
            # For find, we need firstname, lastname, domain
            parts = email.split('@')
            if len(parts) != 2:
                raise ValueError(f"Invalid email format: {email}")
            
            # Extract name parts (simplified - you may need better logic)
            local_part = parts[0]
            domain = parts[1]
            
            # Try to split local part into firstname/lastname
            if '.' in local_part:
                firstname, lastname = local_part.split('.', 1)
            else:
                firstname = local_part
                lastname = ""
            
            result = verifier.find_mail(firstname, lastname, domain)
        
        # Check if still greylisted
        if result.get('greylist', False):
            retry_count += 1
            
            # If we've reached max retries (3), mark as catch-all
            if retry_count >= 3:
                result['catch_all'] = True
                result['greylist'] = False
                result['message'] = "Marked as catch-all after 3 greylist retries"
                result['debug_message'] = f"Max greylist retries ({retry_count}) reached"
                
                # Update MongoDB with final result
                db.update_single_verification(document_id, result, retry_count)
                print(f"Max retries reached for {email}, marked as catch-all")
                return result
            
            # Schedule next retry with progressive delay
            delay = delays[retry_count - 1] if retry_count <= len(delays) else delays[-1]
            
            # Update MongoDB with current attempt
            db.update_single_verification(document_id, result, retry_count)
            
            print(f"Still greylisted, scheduling retry {retry_count + 1} in {delay}s")
            
            # Schedule next retry
            self.retry(
                args=[email, verification_type, document_id, retry_count],
                countdown=delay
            )
        
        else:
            # Success! Update MongoDB with final result
            db.update_single_verification(document_id, result, retry_count)
            print(f"Verification successful for {email} after {retry_count} retries")
            return result
    
    except Exception as exc:
        print(f"Error in greylist retry for {email}: {exc}")
        
        # Update MongoDB with error
        error_result = {
            'error': True,
            'message': f"Error during retry: {str(exc)}",
            'debug_message': f"Celery task failed: {str(exc)}"
        }
        db.update_single_verification(document_id, error_result, retry_count)
        
        # Don't retry on errors, just fail
        raise exc


@celery_app.task
def bulk_verification_with_retries(batch_id: str, emails: list, verification_type: str):
    """
    Process bulk verification with greylist retries
    :param batch_id: Unique batch identifier
    :param emails: List of emails to verify
    :param verification_type: 'verify' or 'find'
    """
    from verifier import MailVerifier
    from models import db
    
    # Progressive delays for bulk retries: 10m, 20m, 30m
    delays = [600, 1200, 1800]  # 10, 20, 30 minutes in seconds
    
    verifier = MailVerifier(['<EMAIL>'])  # Use appropriate sender
    
    # Track emails that need retries
    retry_queue = {}  # {email: retry_count}
    
    def process_email(email_data, retry_count=0):
        """Process a single email with retry logic"""
        if verification_type == 'verify':
            email = email_data if isinstance(email_data, str) else email_data.get('email')
            result = verifier.verify_mail(email)
        else:  # find
            if isinstance(email_data, str):
                # Parse email for find operation
                parts = email_data.split('@')
                firstname = parts[0].split('.')[0] if '.' in parts[0] else parts[0]
                lastname = parts[0].split('.')[1] if '.' in parts[0] else ""
                domain = parts[1]
            else:
                firstname = email_data.get('firstname', '')
                lastname = email_data.get('lastname', '')
                domain = email_data.get('domain', '')
                email = f"{firstname}.{lastname}@{domain}" if lastname else f"{firstname}@{domain}"
            
            result = verifier.find_mail(firstname, lastname, domain)
        
        # Add retry information to result
        result['retry_count'] = retry_count
        result['batch_id'] = batch_id
        
        return email, result
    
    # Process all emails initially
    for email_data in emails:
        try:
            email, result = process_email(email_data)
            
            if result.get('greylist', False):
                # Add to retry queue
                retry_queue[email] = 0
            
            # Save result to MongoDB
            db.add_bulk_result(batch_id, email, result)
            
        except Exception as e:
            error_result = {
                'error': True,
                'message': f"Processing error: {str(e)}",
                'retry_count': 0,
                'batch_id': batch_id
            }
            email = email_data if isinstance(email_data, str) else email_data.get('email', 'unknown')
            db.add_bulk_result(batch_id, email, error_result)
    
    # Process retries with progressive delays
    for delay_index, delay in enumerate(delays):
        if not retry_queue:
            break
        
        print(f"Waiting {delay}s before retry {delay_index + 1} for {len(retry_queue)} emails")
        time.sleep(delay)
        
        current_retry_queue = retry_queue.copy()
        retry_queue.clear()
        
        for email, current_retry_count in current_retry_queue.items():
            try:
                # Find original email data
                email_data = email
                for orig_data in emails:
                    if isinstance(orig_data, str) and orig_data == email:
                        email_data = orig_data
                        break
                    elif isinstance(orig_data, dict) and orig_data.get('email') == email:
                        email_data = orig_data
                        break
                
                email, result = process_email(email_data, current_retry_count + 1)
                
                if result.get('greylist', False) and current_retry_count + 1 < 3:
                    # Still greylisted, add to next retry
                    retry_queue[email] = current_retry_count + 1
                elif result.get('greylist', False) and current_retry_count + 1 >= 3:
                    # Max retries reached, mark as catch-all
                    result['catch_all'] = True
                    result['greylist'] = False
                    result['message'] = "Marked as catch-all after 3 greylist retries"
                
                # Update result in MongoDB
                db.add_bulk_result(batch_id, email, result)
                
            except Exception as e:
                error_result = {
                    'error': True,
                    'message': f"Retry error: {str(e)}",
                    'retry_count': current_retry_count + 1,
                    'batch_id': batch_id
                }
                db.add_bulk_result(batch_id, email, error_result)
    
    # Mark batch as completed
    db.complete_bulk_verification(batch_id)
    print(f"Bulk verification completed for batch {batch_id}")


if __name__ == '__main__':
    celery_app.start()
