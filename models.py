"""
MongoDB models for email verification data storage
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pymongo import MongoClient
from bson import ObjectId
import os


class EmailVerificationDB:
    """MongoDB handler for email verification data"""
    
    def __init__(self, connection_string: str = None):
        """
        Initialize MongoDB connection
        :param connection_string: MongoDB connection string
        """
        if not connection_string:
            connection_string = os.getenv('MONGODB_URL', 'mongodb://localhost:27017/')
        
        self.client = MongoClient(connection_string)
        self.db = self.client.email_verification
        self.single_verifications = self.db.single_verifications
        self.bulk_verifications = self.db.bulk_verifications
        
        # Create indexes for better performance
        self._create_indexes()
    
    def _create_indexes(self):
        """Create database indexes"""
        # Single verifications indexes
        self.single_verifications.create_index("email")
        self.single_verifications.create_index("created_at")
        self.single_verifications.create_index("verification_type")
        self.single_verifications.create_index("greylist_retry_count")
        
        # Bulk verifications indexes
        self.bulk_verifications.create_index("batch_id")
        self.bulk_verifications.create_index("status")
        self.bulk_verifications.create_index("created_at")
    
    def save_single_verification(self, email: str, verification_type: str, 
                                result: Dict[str, Any], greylist_retry_count: int = 0) -> str:
        """
        Save single email verification result
        :param email: Email address
        :param verification_type: 'verify' or 'find'
        :param result: Verification result dictionary
        :param greylist_retry_count: Number of greylist retries
        :return: Document ID
        """
        document = {
            'email': email,
            'verification_type': verification_type,
            'result': result,
            'greylist_retry_count': greylist_retry_count,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow(),
            'is_greylist_retry': greylist_retry_count > 0,
            'final_result': greylist_retry_count >= 3  # Mark as final after 3 retries
        }
        
        result = self.single_verifications.insert_one(document)
        return str(result.inserted_id)
    
    def update_single_verification(self, document_id: str, result: Dict[str, Any], 
                                  greylist_retry_count: int) -> bool:
        """
        Update single verification with retry result
        :param document_id: MongoDB document ID
        :param result: Updated verification result
        :param greylist_retry_count: Updated retry count
        :return: Success status
        """
        update_data = {
            '$set': {
                'result': result,
                'greylist_retry_count': greylist_retry_count,
                'updated_at': datetime.utcnow(),
                'final_result': greylist_retry_count >= 3
            }
        }
        
        result = self.single_verifications.update_one(
            {'_id': ObjectId(document_id)}, 
            update_data
        )
        return result.modified_count > 0
    
    def create_bulk_verification(self, batch_id: str, verification_type: str, 
                               total_emails: int) -> str:
        """
        Create bulk verification batch
        :param batch_id: Unique batch identifier
        :param verification_type: 'verify' or 'find'
        :param total_emails: Total number of emails in batch
        :return: Document ID
        """
        document = {
            'batch_id': batch_id,
            'verification_type': verification_type,
            'total_emails': total_emails,
            'processed_emails': 0,
            'status': 'inprogress',
            'results': [],
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        result = self.bulk_verifications.insert_one(document)
        return str(result.inserted_id)
    
    def add_bulk_result(self, batch_id: str, email: str, result: Dict[str, Any]) -> bool:
        """
        Add result to bulk verification
        :param batch_id: Batch identifier
        :param email: Email address
        :param result: Verification result
        :return: Success status
        """
        update_data = {
            '$push': {
                'results': {
                    'email': email,
                    'result': result,
                    'processed_at': datetime.utcnow()
                }
            },
            '$inc': {'processed_emails': 1},
            '$set': {'updated_at': datetime.utcnow()}
        }
        
        result = self.bulk_verifications.update_one(
            {'batch_id': batch_id}, 
            update_data
        )
        return result.modified_count > 0
    
    def complete_bulk_verification(self, batch_id: str) -> bool:
        """
        Mark bulk verification as completed
        :param batch_id: Batch identifier
        :return: Success status
        """
        update_data = {
            '$set': {
                'status': 'completed',
                'completed_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            }
        }
        
        result = self.bulk_verifications.update_one(
            {'batch_id': batch_id}, 
            update_data
        )
        return result.modified_count > 0
    
    def get_bulk_verification(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """
        Get bulk verification by batch ID
        :param batch_id: Batch identifier
        :return: Verification document or None
        """
        return self.bulk_verifications.find_one({'batch_id': batch_id})
    
    def get_single_verification(self, document_id: str) -> Optional[Dict[str, Any]]:
        """
        Get single verification by document ID
        :param document_id: Document identifier
        :return: Verification document or None
        """
        return self.single_verifications.find_one({'_id': ObjectId(document_id)})


# Global database instance
db = EmailVerificationDB()
