"""
Pydantic models for API request/response bodies
"""

from typing import List, Optional
from pydantic import BaseModel


class BulkVerifyRequestBody(BaseModel):
    """
    Pydantic model for bulk verify post request body.
    """
    emails: List[str]
    webhook_url: Optional[str] = None


class BulkFindRequestBody(BaseModel):
    """
    Pydantic model for bulk find post request body.
    """
    userdetails: List[List[str]]
