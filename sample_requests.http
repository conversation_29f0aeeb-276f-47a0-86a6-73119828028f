### GET request for Single Verify
GET http://localhost:8000/verify-email?email_id=<EMAIL>
Content-Type: application/json


### GET request for Single Find
GET http://localhost:8000/find-email?fname=amin&lname=memon&domain=draftss.com
Content-Type: application/json


### POST request for Bulk Verify
POST http://localhost:8000/bulk-verify-emails
Content-Type: application/json

{
    "emails": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
}


### POST request for Bulk Find
POST http://localhost:8000/bulk-find-emails
Content-Type: application/json

{
    "userdetails": [
        [
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "LennyRachitsky.com"
        ],
        [
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "bannerbear.com"
        ],
        [
            "<PERSON>",
            "<PERSON>",
            "testimonial.to"
        ],
        [
            "<PERSON>",
            "<PERSON><PERSON>",
            "klinger.io"
        ],
        [
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "worklife.vc"
        ],
        [
            "<PERSON>",
            "3",
            "hustlefund.vc"
        ],
        [
            "<PERSON>",
            "<PERSON><PERSON>z",
            "every.to"
        ],
        [
            "<PERSON>n",
            "Biyani",
            "maven.com"
        ],
        [
            "<PERSON>",
            "Yacoubian",
            "copy.ai"
        ],
        [
            "<PERSON>",
            "<PERSON>",
            "chapterone.com"
        ],
        [
            "<PERSON>",
            "Cua",
            "backscoop.com"
        ],
        [
            "<PERSON>",
            "<PERSON>",
            "upmarket.ai"
        ],
        [
            "Mel",
            "Faxon",
            "heymirza.com"
        ],
        [
            "Stephen",
            "Phillips",
            "splashhq.com"
        ],
        [
            "Jesse",
            "King",
            "campwatch.co"
        ],
        [
            "MR",
            "MR",
            "usercentrics.com"
        ],
        [
            "Callum",
            "Mundine",
            "PaperPlan.co"
        ],
        [
            "Jake",
            "Stark",
            "searchbar.world"
        ],
        [
            "Dicky",
            "Davies",
            "sporforya.com"
        ]
    ]
}


### GET request for fetching bulk find results.
GET http://localhost:8000/get-results?task_type=find&task_uid=5ddb1b34-943d-4262-9b71-95214d446e62
Content-Type: application/json


### GET request for fetching bulk verify results.
GET http://localhost:8000/get-results?task_type=verify&task_uid=d75d28db-35b8-4720-999b-8d0fe44faa6a
Content-Type: application/json
