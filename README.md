### Setup For Development:
1. Clone project.
2. Crate 2 folders - **mongodb_data** and **output** - in root of project.
3. Run `docker compose -p "emailfindverify-dev" -f compose.dev.yml up -d`
4. To stop, run: `docker compose -p "emailfindverify-dev" down` 

### Build Images (For both Draftss Demo Server and End Users):
1. Login to gitlab container registry if not already logged in:
   ```shell
   docker login registry.gitlab.com
   ```
2. Build image. Replace **x, y and z** with correct version values:
   ```shell
   docker buildx build --platform linux/amd64 -t registry.gitlab.com/aminmemon/emailfindandverify:vx.y.z .
   ```
3. Push to repo:
   ```shell
   docker push registry.gitlab.com/aminmemon/emailfindandverify:vx.y.z
   ```
4. Generate image file using. Replace **x, y and z** with correct version values. You will find the file in output folder. 
   ```shell
   docker save registry.gitlab.com/aminmemon/emailfindandverify:v0.1.0 | gzip > output/email_find_verify_0_1_0.tar.gz
   ``` 

### Preparing Compose File for New User:
1. Duplicate one of the existing compose file in `release` folder.
2. Change the following details:
   - **fastapi** service image version (if not latest).
   - domain (Host) value for **fastapi** service.
   - FINDVERIFY_VERIFIER_EMAILS env variable value for **fastapi** service.
   - **celery-worker** service image version (if not latest).
   - FINDVERIFY_VERIFIER_EMAILS env variable for **celery-worker** service.
   - hostname value of **mailserver** service.
   - SSL_DOMAIN env variable value of **mailserver** service.
   - POSTMASTER_ADDRESS env variable value of **mailserver** service.
   - domain (Host) value for **mailserver** service.
   - certresolver email value in **traefik** service.
   - Uncomment the staging ca server for testing first time deployment.


### Server Setup for Deployment:
1. Go to Linode dashboard firewall settings for this server and add the below shown rules:
![img.png](readme_images/img.png)
2. Create new user named `emailfindverify` and add them to sudoers group.
3. Change hostname of server to the one in compose file.
4. Install Docker.
5. Create the following folders in user home directory (including sub-folders):
   - images
   - mailserver/dms/mail-data/
   - mailserver/dms/mail-state/
   - mailserver/dms/mail-logs/
   - mailserver/dms/config/
   - mongodb_data
   - traefik/logs/
   - traefik/letsencrypt/
6. Inside `/traefik/letsencrypt/` create an empty file named `acme.json` and set it's permission to 600.
7. Copy the image file into images folder and use `docker load`.
8. Now for setting up email related records, please refer to [this](https://docker-mailserver.github.io/docker-mailserver/latest/usage/#minimal-dns-setup) and follow the below steps:
   1. Create A record (ipv4) for server domain and mail domain and point both to the server ip address. For example if server domain is **verifier.example.com** then mail domain should be **mail.verifier.example.com**.
   2. Create AAAA record (ipv6) for the mail domain and point it to the ipv6 address of server.
   3. Create MX record using the mail domain.
   4. Set up reverse dns (RDNS) for both ipv4 and ipv6 by going to Linode dashbaord > Linodes > Select the server > Network tab > Edit RDNS in IP Address table at the very bottom.
   5. Proceed with **Deployment Process** below and set up SPF, DKIM and DMARC at the end. 

### Deployment Process: 
1. Copy the compose file you created to the user home directory, alongside the folders you created in previous steps.
2. Run the following to deploy the containers:
   ```shell
   sudo docker compose -p "emailfindverify-dev" -f <compose_file_name> up -d
   ```
3. Once deployment is complete immediately run the below command to create an email id with password. This needs to be done within 120 seconds after deployment (required by mailserver container for starting some service). Failing to do so will stop the mailserver container.
   ```shell
   sudo docker exec -ti mailserver setup email add <email_id> <password>
   ```
4. Check logs of both **traefik** and **mailserver** to make sure there are no errors.
   ```shell
   sudo docker logs -f --tail 100 <container_name>
   ```
   NOTE: There might be an error in traefik regarding letsencrypt (splitting of port and host). I'm not sure why that happens but afaik it can be ignored safely, since the certificate gets generated anyways.
5. Visit Traefik dashboard at `http://<server_ip>:8080` to check if everything (routes, services, TLS) is being shown correctly.  
6. Check if acme.json file has been populated correctly.
7. Try logging into the mail server using some client like Mozilla Thunderbird or Microsoft Outlook. Try sending and receiving emails. Once it's time to hand over make sure to delete any test emails and log out.
8. (Optional but recommended) Remove the port 8080 rule (traefik dashboard) from Linode firewall settings.
9. Now for setting up SPF, DKIM and DMARC - refer to https://docker-mailserver.github.io/docker-mailserver/latest/config/best-practices/dkim_dmarc_spf/.
10. Check if all configurations are proper. Done!
